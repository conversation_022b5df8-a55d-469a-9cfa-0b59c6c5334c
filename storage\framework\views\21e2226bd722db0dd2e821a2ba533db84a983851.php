


<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="conten container-fluid mb-8">

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" method="GET">
            <input type="hidden" name="from" value="<?php echo e(request()->from); ?>">
            <input type="hidden" name="to" value="<?php echo e(request()->to); ?>">
            
            <button id="js-daterangepicker-predefined" type="button" class="btn btn-white me-2">
              <i class="bi-calendar-week me-1"></i>
              <span class="js-daterangepicker-predefined-preview"></span>
            </button>

            <div class="input-group-append">
                <button type="submit" class="btn btn-white">
                    Search
                </button>
            </div>

        </form>
    </div>

    <?php $expiryItems = \Facades\App\Cache\Repo::expiringItems() ?>
    <h4 class="m-2">
        Item About to expire
    </h4>

    <ul class="list-group list-group-flush navbar-card-list-group">
        <?php $__currentLoopData = $expiryItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expiryItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="list-group-item form-check-select">
          <div class="row">
            <div class="col-auto">
              <div class="d-flex align-items-center">
                <div class="form-check">
                  <!-- <input class="form-check-input" type="checkbox" value="" id="notificationCheck1" checked> -->
                  <label class="form-check-label" for="notificationCheck1"></label>
                  <span class="form-check-stretched-bg"></span>
                </div>
                <img class="avatar avatar-sm avatar-circle" src="<?php echo e($expiryItem->image); ?>" alt="Image Description">
              </div>
            </div>

            <div class="col ms-n2">
              <h5 class="mb-1"><?php echo e($expiryItem->name); ?></h5>
              <p class="text-body fs-5">
              <a href="/items/<?php echo e($expiryItem->id); ?>/edit" class="badge bg-success">Edit</a></p>
              <?php echo e($expiryItem->reference_number); ?>

            </div>

            <small class="col-auto text-muted text-cap">
              <?php echo e($expiryItem->date_to ? $expiryItem->date_to->diffForHumans() :''); ?>

            </small>
          </div>

          <a class="stretched-link" href="/items/<?php echo e($expiryItem->id); ?>/edit"></a>
        </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </ul>

</div>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/notifications/index.blade.php ENDPATH**/ ?>