<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Models\Item;
use App\Models\Supplier;
use Illuminate\Http\Request;
use App\Http\Requests\SupplierStoreRequest;
use App\Http\Requests\SupplierUpdateRequest;
use Illuminate\Support\Facades\Hash;

class SupplierController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Supplier::class);
        $suppliers = User::search(request()->search)
                ->whereHas("roles", function($q){ $q->where('name', 'supplier'); })
                ->latest()
                ->get();
        return view('app.suppliers.index', compact('suppliers'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Supplier::class);

        return view('app.suppliers.create');
    }

    /**
     * @param \App\Http\Requests\SupplierStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(SupplierStoreRequest $request)
    {
        $this->authorize('create', Supplier::class);

        $validated = $request->validated();

        $validated['password'] = Hash::make(request()->password ?? 'password');
        $supplier = User::create($validated);
        $role = Role::where("name", "supplier")->first();
        if($role && $supplier) $supplier->assignRole($role);

        return redirect()
            ->route('suppliers.show', $supplier)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Supplier $supplier)
    {
        $this->authorize('view', $supplier);
        $item = Item::find($request->item_id);
        return view('app.suppliers.show', compact('supplier', 'item'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Supplier $supplier)
    {
        $this->authorize('update', $supplier);

        return view('app.suppliers.edit', compact('supplier'));
    }

    /**
     * @param \App\Http\Requests\SupplierUpdateRequest $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function update(SupplierUpdateRequest $request, Supplier $supplier)
    {
        $this->authorize('update', $supplier);

        $validated = $request->validated();
        if (empty($validated['password'])) {
            unset($validated['password']);
        } else {
            $validated['password'] = Hash::make($validated['password']);
        }
        $supplier->update($validated);
        return redirect()
            ->route('suppliers.edit', $supplier)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Supplier $supplier)
    {
        $this->authorize('delete', $supplier);

        $supplier->delete();

        return redirect()
            ->route('suppliers.index')
            ->withSuccess(__('crud.common.removed'));
    }

    /**
     * Modernized admin index view for suppliers
     */
    public function modernizedIndex(Request $request)
    {
        $this->authorize('view-any', Supplier::class);

        $suppliers = User::search(request()->search)
                ->whereHas("roles", function($q){ $q->where('name', 'supplier'); })
                ->latest()
                ->paginate(15);

        return view('admin.modernized-suppliers.index', compact('suppliers'));
    }

    /**
     * Modernized admin create view for suppliers
     */
    public function modernizedCreate(Request $request)
    {
        $this->authorize('create', Supplier::class);

        return view('admin.modernized-suppliers.create');
    }

    /**
     * Modernized admin store method for suppliers
     */
    public function modernizedStore(Request $request)
    {
        $this->authorize('create', Supplier::class);

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'code' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email'],
            'phone' => ['nullable', 'string', 'max:255'],
            'address' => ['nullable', 'string', 'max:1000'],
        ]);

        // Set default password
        $validated['password'] = Hash::make('password');

        $supplier = User::create($validated);
        $role = Role::where("name", "supplier")->first();
        if($role && $supplier) $supplier->assignRole($role);

        return redirect()
            ->route('suppliers.show', $supplier)
            ->withSuccess('Supplier created successfully!');
    }

    /**
     * Modernized admin show view for suppliers
     */
    public function modernizedShow(Request $request, Supplier $supplier)
    {
        $this->authorize('view', $supplier);

        return view('admin.modernized-suppliers.show', compact('supplier'));
    }

    /**
     * Modernized admin edit view for suppliers
     */
    public function modernizedEdit(Request $request, Supplier $supplier)
    {
        $this->authorize('update', $supplier);

        return view('admin.modernized-suppliers.edit', compact('supplier'));
    }

    /**
     * Modernized admin update method for suppliers
     */
    public function modernizedUpdate(Request $request, Supplier $supplier)
    {
        $this->authorize('update', $supplier);

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'code' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email,' . $supplier->id],
            'phone' => ['nullable', 'string', 'max:255'],
            'address' => ['nullable', 'string', 'max:1000'],
        ]);

        $supplier->update($validated);

        return redirect()
            ->route('suppliers.show', $supplier)
            ->withSuccess('Supplier updated successfully!');
    }

    /**
     * Modernized admin destroy method for suppliers
     */
    public function modernizedDestroy(Request $request, Supplier $supplier)
    {
        $this->authorize('delete', $supplier);

        $supplier->delete();

        return redirect()
            ->route('suppliers.index')
            ->withSuccess('Supplier deleted successfully!');
    }
}
